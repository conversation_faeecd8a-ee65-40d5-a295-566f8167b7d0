import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import requests
import json
import os
import threading
from PIL import Image, ImageTk
import re

class ImageComparisonTool:
    def __init__(self, root):
        self.root = root
        self.root.title("图像对比工具 - Grok-2-Vision")
        self.root.geometry("1100x700")
        self.root.minsize(1000, 650)
        
        # 设置窗口居中
        self.center_window()
        
        # 设置样式
        self.setup_styles()
        
        # 加载配置
        self.config = self.load_config()
        
        # API配置
        self.api_url = "https://api.x.ai/v1/chat/completions"
        
        # 创建界面
        self.create_widgets()
        
        # 加载保存的配置
        self.load_saved_config()
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_styles(self):
        """设置现代化样式"""
        style = ttk.Style()
        
        # 设置主题
        try:
            style.theme_use('clam')
        except:
            style.theme_use('default')
        
        # 自定义样式
        style.configure('Title.TLabel',
                       font=('Microsoft YaHei UI', 14, 'bold'),
                       foreground='#2c3e50')

        style.configure('Subtitle.TLabel',
                       font=('Microsoft YaHei UI', 9),
                       foreground='#34495e')

        # 修复LabelFrame样式
        style.configure('Header.TLabelframe.Label',
                       font=('Microsoft YaHei UI', 10, 'bold'),
                       foreground='#2c3e50')

        style.configure('Custom.TButton',
                       font=('Microsoft YaHei UI', 9),
                       padding=(8, 3))

        style.configure('Action.TButton',
                       font=('Microsoft YaHei UI', 10, 'bold'),
                       padding=(15, 6))
        
        # 设置颜色
        style.configure('Success.TLabel', foreground='#27ae60')
        style.configure('Error.TLabel', foreground='#e74c3c')
        style.configure('Info.TLabel', foreground='#3498db')
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            return {
                "api_key": "",
                "system_prompt": "请对比这两张图片，详细描述它们的差异和相似之处。",
                "batch_mode": {
                    "folder_a": "",
                    "folder_b": ""
                }
            }
    
    def save_config(self):
        """保存配置到文件"""
        with open("config.json", "w", encoding="utf-8") as f:
            json.dump(self.config, f, ensure_ascii=False, indent=4)
    
    def load_saved_config(self):
        """加载保存的配置到界面"""
        self.api_key_entry.delete(0, tk.END)
        self.api_key_entry.insert(0, self.config.get("api_key", ""))
        
        self.prompt_text.delete(1.0, tk.END)
        self.prompt_text.insert(1.0, self.config.get("system_prompt", ""))
        
        if self.config.get("batch_mode"):
            self.folder_a_entry.delete(0, tk.END)
            self.folder_a_entry.insert(0, self.config["batch_mode"].get("folder_a", ""))
            self.folder_b_entry.delete(0, tk.END)
            self.folder_b_entry.insert(0, self.config["batch_mode"].get("folder_b", ""))
    
    def create_widgets(self):
        """创建现代化GUI界面"""
        # 主滚动框架
        self.create_scrollable_frame()
        
        # 标题区域
        self.create_header()
        
        # 配置区域
        self.create_config_section()
        
        # 模式选择区域
        self.create_mode_section()
        
        # 工作区域
        self.create_work_section()
        
        # 操作按钮区域
        self.create_action_section()
        
        # 底部信息区域
        self.create_info_section()

        # 绑定拖拽事件
        self.setup_drag_drop()
    
    def create_scrollable_frame(self):
        """创建可滚动的主框架"""
        # 创建外层容器
        container = ttk.Frame(self.root)
        container.pack(fill="both", expand=True, padx=6, pady=6)
        
        # 创建画布和滚动条
        self.canvas = tk.Canvas(container, highlightthickness=0)
        self.v_scrollbar = ttk.Scrollbar(container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        # 配置滚动
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.v_scrollbar.set)
        
        # 布局画布和滚动条
        self.canvas.pack(side="left", fill="both", expand=True)
        self.v_scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮事件
        def on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        self.canvas.bind_all("<MouseWheel>", on_mousewheel)
        
        # 绑定画布大小变化事件
        def on_canvas_configure(event):
            self.canvas.itemconfig(self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw"),
                                 width=event.width)
        
        self.canvas.bind("<Configure>", on_canvas_configure)
        
        # 设置主框架为可滚动框架
        self.main_frame = self.scrollable_frame
    
    def create_header(self):
        """创建标题区域"""
        header_frame = ttk.Frame(self.main_frame)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        header_frame.columnconfigure(0, weight=1)

        # 主标题
        title_label = ttk.Label(header_frame, text="图像对比工具", style='Title.TLabel')
        title_label.grid(row=0, column=0)

        # 副标题
        subtitle_label = ttk.Label(header_frame, text="基于 Grok-2-Vision API 的智能图像对比分析工具", style='Subtitle.TLabel')
        subtitle_label.grid(row=1, column=0, pady=(3, 0))

        # 分隔线
        separator = ttk.Separator(header_frame, orient='horizontal')
        separator.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(8, 0))
    
    def create_config_section(self):
        """创建配置区域"""
        config_frame = ttk.LabelFrame(self.main_frame, text="", padding="10")
        config_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 12))
        config_frame.columnconfigure(1, weight=1)

        # API密钥配置
        api_label = ttk.Label(config_frame, text="API 密钥:", font=('Microsoft YaHei UI', 9, 'bold'))
        api_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 8), pady=(0, 6))

        self.api_key_entry = ttk.Entry(config_frame, show="*", font=('Consolas', 9))
        self.api_key_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 6))

        # 系统提示词配置
        prompt_label = ttk.Label(config_frame, text="系统提示词:", font=('Microsoft YaHei UI', 9, 'bold'))
        prompt_label.grid(row=1, column=0, sticky=(tk.W, tk.N), padx=(0, 8))

        prompt_frame = ttk.Frame(config_frame)
        prompt_frame.grid(row=1, column=1, sticky=(tk.W, tk.E))
        prompt_frame.columnconfigure(0, weight=1)

        self.prompt_text = scrolledtext.ScrolledText(prompt_frame, height=6, wrap=tk.WORD,
                                                    font=('Microsoft YaHei UI', 8))
        self.prompt_text.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # 设置固定高度为100px
        prompt_frame.configure(height=100)
        prompt_frame.grid_propagate(False)
    
    def create_mode_section(self):
        """创建模式选择区域 - 现在使用TAB样式"""
        # 创建工作区域的TAB容器
        self.work_notebook = ttk.Notebook(self.main_frame)
        self.work_notebook.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 12))

        # 创建单例模式TAB
        self.single_frame = ttk.Frame(self.work_notebook)
        self.work_notebook.add(self.single_frame, text="🖼️ 单例模式")

        # 创建批量模式TAB
        self.batch_frame = ttk.Frame(self.work_notebook)
        self.work_notebook.add(self.batch_frame, text="📁 批量模式")

        # 绑定TAB切换事件
        self.work_notebook.bind("<<NotebookTabChanged>>", self.on_tab_change)
    
    def create_work_section(self):
        """创建工作区域内容"""
        # 创建单例模式内容
        self.create_single_mode_content()

        # 创建批量模式内容
        self.create_batch_mode_content()

    def create_single_mode_content(self):
        """创建单例模式TAB内容"""
        # 创建主容器，包含图片区域和按钮区域
        main_container = ttk.Frame(self.single_frame)
        main_container.pack(fill="both", expand=True, padx=12, pady=12)
        main_container.columnconfigure(0, weight=1)

        # 创建两列布局
        images_container = ttk.Frame(main_container)
        images_container.grid(row=0, column=0, sticky=(tk.W, tk.E))
        images_container.columnconfigure(0, weight=1)
        images_container.columnconfigure(1, weight=1)
        images_container.columnconfigure(2, weight=0)  # 按钮列

        # 图片A区域
        image_a_frame = ttk.LabelFrame(images_container, text="图片 A", padding="10")
        image_a_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 6))
        image_a_frame.columnconfigure(0, weight=1)

        # 图片A选择
        a_select_frame = ttk.Frame(image_a_frame)
        a_select_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 6))
        a_select_frame.columnconfigure(0, weight=1)

        self.image_a_entry = ttk.Entry(a_select_frame, font=('Microsoft YaHei UI', 8))
        self.image_a_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 6))

        ttk.Button(a_select_frame, text="选择文件", command=self.browse_image_a,
                  style='Custom.TButton').grid(row=0, column=1)

        # 图片A预览
        self.image_a_preview = ttk.Label(image_a_frame, text="点击选择图片A",
                                        background='#f8f9fa', relief='ridge',
                                        anchor='center', padding=12)
        self.image_a_preview.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 6))
        self.image_a_preview.configure(width=25)  # 限制预览区域宽度

        # 图片B区域
        image_b_frame = ttk.LabelFrame(images_container, text="图片 B", padding="10")
        image_b_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(6, 12))
        image_b_frame.columnconfigure(0, weight=1)

        # 图片B选择
        b_select_frame = ttk.Frame(image_b_frame)
        b_select_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 6))
        b_select_frame.columnconfigure(0, weight=1)

        self.image_b_entry = ttk.Entry(b_select_frame, font=('Microsoft YaHei UI', 8))
        self.image_b_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 6))

        ttk.Button(b_select_frame, text="选择文件", command=self.browse_image_b,
                  style='Custom.TButton').grid(row=0, column=1)

        # 图片B预览
        self.image_b_preview = ttk.Label(image_b_frame, text="点击选择图片B",
                                        background='#f8f9fa', relief='ridge',
                                        anchor='center', padding=12)
        self.image_b_preview.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 6))
        self.image_b_preview.configure(width=25)  # 限制预览区域宽度

        # 右侧按钮区域
        button_frame = ttk.Frame(images_container)
        button_frame.grid(row=0, column=2, sticky=(tk.N, tk.S), padx=(12, 0))

        # 开始对比分析按钮
        self.generate_button = ttk.Button(button_frame, text="🚀 开始对比分析",
                                         command=self.generate_comparison,
                                         style='Action.TButton')
        self.generate_button.pack(anchor='n', pady=(20, 0))

        # 进度状态
        self.progress_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(button_frame, textvariable=self.progress_var,
                                     style='Info.TLabel')
        self.status_label.pack(anchor='n', pady=(10, 0))
        
    def create_batch_mode_content(self):
        """创建批量模式TAB内容"""
        # 创建主容器
        main_container = ttk.Frame(self.batch_frame)
        main_container.pack(fill="both", expand=True, padx=12, pady=12)
        main_container.columnconfigure(0, weight=1)

        # 目录选择区域
        dirs_container = ttk.Frame(main_container)
        dirs_container.grid(row=0, column=0, sticky=(tk.W, tk.E))
        dirs_container.columnconfigure(0, weight=1)
        dirs_container.columnconfigure(1, weight=0)  # 按钮列

        # 目录选择
        dirs_frame = ttk.Frame(dirs_container)
        dirs_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 12))
        dirs_frame.columnconfigure(1, weight=1)

        # 目录A
        ttk.Label(dirs_frame, text="图片A目录:", font=('Microsoft YaHei UI', 9, 'bold')).grid(
            row=0, column=0, sticky=tk.W, padx=(0, 8), pady=(0, 8))

        folder_a_frame = ttk.Frame(dirs_frame)
        folder_a_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 8))
        folder_a_frame.columnconfigure(0, weight=1)

        self.folder_a_entry = ttk.Entry(folder_a_frame, font=('Microsoft YaHei UI', 8))
        self.folder_a_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 6))

        ttk.Button(folder_a_frame, text="选择目录", command=self.browse_folder_a,
                  style='Custom.TButton').grid(row=0, column=1)

        # 目录B
        ttk.Label(dirs_frame, text="图片B目录:", font=('Microsoft YaHei UI', 9, 'bold')).grid(
            row=1, column=0, sticky=tk.W, padx=(0, 8))

        folder_b_frame = ttk.Frame(dirs_frame)
        folder_b_frame.grid(row=1, column=1, sticky=(tk.W, tk.E))
        folder_b_frame.columnconfigure(0, weight=1)

        self.folder_b_entry = ttk.Entry(folder_b_frame, font=('Microsoft YaHei UI', 8))
        self.folder_b_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 6))

        ttk.Button(folder_b_frame, text="选择目录", command=self.browse_folder_b,
                  style='Custom.TButton').grid(row=0, column=1)

        # 批量处理说明
        batch_info = ttk.Label(dirs_frame,
                              text="💡 批量模式将自动匹配同名文件进行对比，结果保存为txt文件",
                              style='Subtitle.TLabel')
        batch_info.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))

        # 右侧按钮区域
        button_frame = ttk.Frame(dirs_container)
        button_frame.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 开始批量处理按钮
        self.batch_generate_button = ttk.Button(button_frame, text="🚀 开始批量处理",
                                               command=self.generate_comparison,
                                               style='Action.TButton')
        self.batch_generate_button.pack(anchor='n', pady=(20, 0))

        # 进度状态（批量模式共享相同的状态变量）
        self.batch_status_label = ttk.Label(button_frame, textvariable=self.progress_var,
                                           style='Info.TLabel')
        self.batch_status_label.pack(anchor='n', pady=(10, 0))
    
    def create_action_section(self):
        """创建操作按钮区域 - 现在按钮已移到TAB中"""
        pass  # 按钮现在在各个TAB中
    
    def create_info_section(self):
        """创建信息显示区域"""
        # 创建信息区域框架
        info_frame = ttk.LabelFrame(self.main_frame, text="", padding="10")
        info_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 12))
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(0, weight=1)

        # 创建标签页
        notebook = ttk.Notebook(info_frame)
        notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 执行日志标签页
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="📋 执行日志")
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, wrap=tk.WORD,
                                                 font=('Consolas', 8), state='disabled')
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=6, pady=6)

        # 对比结果标签页
        result_frame = ttk.Frame(notebook)
        notebook.add(result_frame, text="📊 对比结果")
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)

        self.result_text = scrolledtext.ScrolledText(result_frame, height=8, wrap=tk.WORD,
                                                    font=('Microsoft YaHei UI', 9))
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=6, pady=6)
        
        # 配置网格权重 - 确保信息区域能够扩展
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.rowconfigure(3, weight=1)
    
    def setup_drag_drop(self):
        """设置拖拽功能"""
        # 为预览区域添加点击事件，方便用户选择图片
        self.image_a_preview.bind("<Button-1>", lambda e: self.browse_image_a())
        self.image_b_preview.bind("<Button-1>", lambda e: self.browse_image_b())
        
        # 添加提示文本
        self.image_a_preview.configure(text="点击此处选择图片A")
        self.image_b_preview.configure(text="点击此处选择图片B")
    
    def update_image_preview(self, image_path, preview_label):
        """更新图片预览"""
        try:
            image = Image.open(image_path)
            # 调整图片大小用于预览，限制最大尺寸
            image.thumbnail((100, 100), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image)
            
            # 设置图片并调整标签大小
            preview_label.configure(image=photo, text="", compound='center')
            preview_label.image = photo  # 保持引用
            
            # 显示图片信息
            file_name = os.path.basename(image_path)
            file_size = os.path.getsize(image_path) // 1024  # KB
            info_text = f"{file_name}\n({file_size} KB)"
            
            # 在图片下方显示文件信息
            if hasattr(preview_label, 'info_label'):
                preview_label.info_label.configure(text=info_text)
            else:
                # 创建信息标签
                parent = preview_label.master
                preview_label.info_label = ttk.Label(parent, text=info_text, 
                                                   font=('Microsoft YaHei UI', 8),
                                                   foreground='#666666', anchor='center')
                
                # 获取预览标签的网格信息并在其下方放置信息标签
                grid_info = preview_label.grid_info()
                row = grid_info.get('row', 0)
                preview_label.info_label.grid(row=row+1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
                
        except Exception as e:
            preview_label.configure(image="", text=f"预览失败\n{str(e)}")
            # 清除信息标签
            if hasattr(preview_label, 'info_label'):
                preview_label.info_label.configure(text="")
    
    def on_tab_change(self, event):
        """TAB切换处理"""
        selected_tab = event.widget.tab('current')['text']
        if "单例模式" in selected_tab:
            self.progress_var.set("就绪 - 请选择两张图片进行对比")
        else:
            self.progress_var.set("就绪 - 请选择两个包含图片的目录")

    def on_mode_change(self):
        """保留此方法以兼容性，但现在使用TAB切换"""
        pass
    
    def browse_image_a(self):
        """浏览选择图片A"""
        file_path = filedialog.askopenfilename(
            title="选择图片A",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.bmp *.gif")]
        )
        if file_path:
            self.image_a_entry.delete(0, tk.END)
            self.image_a_entry.insert(0, file_path)
            self.update_image_preview(file_path, self.image_a_preview)
    
    def browse_image_b(self):
        """浏览选择图片B"""
        file_path = filedialog.askopenfilename(
            title="选择图片B",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.bmp *.gif")]
        )
        if file_path:
            self.image_b_entry.delete(0, tk.END)
            self.image_b_entry.insert(0, file_path)
            self.update_image_preview(file_path, self.image_b_preview)
    
    def browse_folder_a(self):
        """浏览选择目录A"""
        folder_path = filedialog.askdirectory(title="选择图片A目录")
        if folder_path:
            self.folder_a_entry.delete(0, tk.END)
            self.folder_a_entry.insert(0, folder_path)
            # 保存配置
            self.config["batch_mode"]["folder_a"] = folder_path
            self.save_config()
    
    def browse_folder_b(self):
        """浏览选择目录B"""
        folder_path = filedialog.askdirectory(title="选择图片B目录")
        if folder_path:
            self.folder_b_entry.delete(0, tk.END)
            self.folder_b_entry.insert(0, folder_path)
            # 保存配置
            self.config["batch_mode"]["folder_b"] = folder_path
            self.save_config()
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # 设置不同级别的日志颜色
        level_colors = {
            "INFO": "#2c3e50",
            "SUCCESS": "#27ae60", 
            "ERROR": "#e74c3c",
            "WARNING": "#f39c12"
        }
        
        level_icons = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "ERROR": "❌", 
            "WARNING": "⚠️"
        }
        
        # 启用文本框编辑
        self.log_text.config(state='normal')
        
        # 插入时间戳和图标
        self.log_text.insert(tk.END, f"[{timestamp}] {level_icons.get(level, 'ℹ️')} {message}\n")
        
        # 配置颜色标签
        start_line = self.log_text.index("end-2c linestart")
        end_line = self.log_text.index("end-1c")
        
        tag_name = f"{level}_{timestamp}"
        self.log_text.tag_add(tag_name, start_line, end_line)
        self.log_text.tag_config(tag_name, foreground=level_colors.get(level, "#2c3e50"))
        
        # 滚动到底部
        self.log_text.see(tk.END)
        
        # 禁用文本框编辑
        self.log_text.config(state='disabled')
        
        # 更新界面
        self.root.update()
    
    def save_config_from_ui(self):
        """从界面保存配置"""
        self.config["api_key"] = self.api_key_entry.get()
        self.config["system_prompt"] = self.prompt_text.get(1.0, tk.END).strip()
        if self.mode_var.get() == "batch":
            self.config["batch_mode"]["folder_a"] = self.folder_a_entry.get()
            self.config["batch_mode"]["folder_b"] = self.folder_b_entry.get()
        self.save_config()
    
    def call_grok_api(self, image_a_path, image_b_path, prompt):
        """调用Grok API"""
        try:
            import base64
            
            # 读取图片文件并编码为base64
            with open(image_a_path, 'rb') as f:
                image_a_data = base64.b64encode(f.read()).decode('utf-8')
            
            with open(image_b_path, 'rb') as f:
                image_b_data = base64.b64encode(f.read()).decode('utf-8')
            
            # 获取图片MIME类型
            def get_mime_type(file_path):
                ext = os.path.splitext(file_path)[1].lower()
                mime_types = {
                    '.png': 'image/png',
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.bmp': 'image/bmp',
                    '.gif': 'image/gif'
                }
                return mime_types.get(ext, 'image/png')
            
            mime_a = get_mime_type(image_a_path)
            mime_b = get_mime_type(image_b_path)
            
            # 准备请求数据
            headers = {
                "Authorization": f"Bearer {self.config['api_key']}",
                "Content-Type": "application/json"
            }
            
            # 构建消息 - 按照Grok API的正确格式
            messages = [
                {
                    "role": "system",
                    "content": prompt
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请对比分析这两张图片："
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:{mime_a};base64,{image_a_data}"
                            }
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:{mime_b};base64,{image_b_data}"
                            }
                        }
                    ]
                }
            ]
            
            data = {
                "model": "grok-2-vision-1212",
                "messages": messages,
                "max_tokens": 1000
            }
            
            # 发送请求
            self.log_message("正在发送请求到Grok API...", "INFO")
            self.log_message(f"使用模型: {data['model']}", "INFO")
            self.log_message(f"图片A大小: {len(image_a_data)} 字符", "INFO")
            self.log_message(f"图片B大小: {len(image_b_data)} 字符", "INFO")
            
            response = requests.post(self.api_url, headers=headers, json=data, timeout=60)
            
            self.log_message(f"API响应状态码: {response.status_code}", "INFO")
            
            if response.status_code == 200:
                result = response.json()
                # 检查响应结构
                if "choices" in result and len(result["choices"]) > 0:
                    if "message" in result["choices"][0] and "content" in result["choices"][0]["message"]:
                        content = result["choices"][0]["message"]["content"]
                        self.log_message(f"API调用成功，返回内容长度: {len(content)} 字符", "SUCCESS")
                        return content
                    else:
                        self.log_message("API响应格式异常", "ERROR")
                        return f"API响应格式异常: {result}"
                else:
                    self.log_message("API响应格式异常", "ERROR")
                    return f"API响应格式异常: {result}"
            else:
                error_detail = response.text
                self.log_message(f"API调用失败: {response.status_code}", "ERROR")
                return f"API调用失败: {response.status_code} - {error_detail}"
                
        except Exception as e:
            return f"API调用异常: {str(e)}"
    
    def generate_comparison(self):
        """生成对比结果"""
        # 保存配置
        self.save_config_from_ui()
        
        # 检查API密钥
        if not self.config["api_key"]:
            messagebox.showerror("错误", "请输入API密钥")
            self.progress_var.set("错误 - 请输入API密钥")
            return
        
        # 检查提示词
        if not self.config["system_prompt"]:
            messagebox.showerror("错误", "请输入系统提示词")
            self.progress_var.set("错误 - 请输入系统提示词")
            return
        
        # 更新按钮和状态
        current_tab = self.work_notebook.tab(self.work_notebook.select(), "text")
        if "单例模式" in current_tab:
            self.generate_button.config(state="disabled", text="🔄 处理中...")
        else:
            self.batch_generate_button.config(state="disabled", text="🔄 处理中...")
        self.progress_var.set("正在准备...")
        
        # 清空日志和结果
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')
        self.result_text.delete(1.0, tk.END)

        # 检测当前选中的TAB
        current_tab = self.work_notebook.tab(self.work_notebook.select(), "text")
        if "单例模式" in current_tab:
            # 单例模式
            self.progress_var.set("正在处理单例模式...")
            threading.Thread(target=self.single_mode_process, daemon=True).start()
        else:
            # 批量模式
            self.progress_var.set("正在处理批量模式...")
            threading.Thread(target=self.batch_mode_process, daemon=True).start()
    
    def single_mode_process(self):
        """单例模式处理"""
        try:
            image_a_path = self.image_a_entry.get().strip()
            image_b_path = self.image_b_entry.get().strip()
            
            if not image_a_path or not image_b_path:
                self.log_message("请选择两张图片", "ERROR")
                self.progress_var.set("错误 - 请选择两张图片")
                return
            
            if not os.path.exists(image_a_path):
                self.log_message(f"图片A不存在: {image_a_path}", "ERROR")
                self.progress_var.set("错误 - 图片A不存在")
                return
            
            if not os.path.exists(image_b_path):
                self.log_message(f"图片B不存在: {image_b_path}", "ERROR")
                self.progress_var.set("错误 - 图片B不存在")
                return
            
            self.log_message("开始处理单例模式", "INFO")
            self.log_message(f"图片A: {os.path.basename(image_a_path)}", "INFO")
            self.log_message(f"图片B: {os.path.basename(image_b_path)}", "INFO")
            self.progress_var.set("正在调用API...")
            
            # 调用API
            result = self.call_grok_api(image_a_path, image_b_path, self.config["system_prompt"])
            
            if result.startswith("API调用失败") or result.startswith("API调用异常"):
                self.log_message("API调用失败", "ERROR")
                self.progress_var.set("处理失败")
            else:
                self.log_message("单例模式处理完成", "SUCCESS")
                self.result_text.insert(tk.END, result)
                self.progress_var.set("处理完成")
            
        except Exception as e:
            self.log_message(f"处理异常: {str(e)}", "ERROR")
            self.progress_var.set("处理异常")
        finally:
            self.generate_button.config(state="normal", text="🚀 开始对比分析")
    
    def batch_mode_process(self):
        """批量模式处理"""
        try:
            folder_a = self.folder_a_entry.get().strip()
            folder_b = self.folder_b_entry.get().strip()
            
            if not folder_a or not folder_b:
                self.log_message("错误: 请选择两个目录")
                return
            
            if not os.path.exists(folder_a):
                self.log_message(f"错误: 目录A不存在 - {folder_a}")
                return
            
            if not os.path.exists(folder_b):
                self.log_message(f"错误: 目录B不存在 - {folder_b}")
                return
            
            self.log_message("开始处理批量模式...")
            self.log_message(f"目录A: {folder_a}")
            self.log_message(f"目录B: {folder_b}")
            
            # 获取目录中的图片文件
            image_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif')
            
            files_a = [f for f in os.listdir(folder_a) if f.lower().endswith(image_extensions)]
            files_b = [f for f in os.listdir(folder_b) if f.lower().endswith(image_extensions)]
            
            if not files_a:
                self.log_message("错误: 目录A中没有找到图片文件")
                return
            
            if not files_b:
                self.log_message("错误: 目录B中没有找到图片文件")
                return
            
            # 按文件名排序
            files_a.sort()
            files_b.sort()
            
            self.log_message(f"目录A中找到 {len(files_a)} 个图片文件")
            self.log_message(f"目录B中找到 {len(files_b)} 个图片文件")
            
            # 处理匹配的文件
            processed_count = 0
            for i, (file_a, file_b) in enumerate(zip(files_a, files_b)):
                # 检查文件名是否匹配（去掉扩展名）
                name_a = os.path.splitext(file_a)[0]
                name_b = os.path.splitext(file_b)[0]
                
                if name_a != name_b:
                    self.log_message(f"跳过: 文件名不匹配 {file_a} vs {file_b}")
                    continue
                
                image_a_path = os.path.join(folder_a, file_a)
                image_b_path = os.path.join(folder_b, file_b)
                
                self.log_message(f"处理第 {i+1} 组: {file_a} vs {file_b}")
                
                # 调用API
                result = self.call_grok_api(image_a_path, image_b_path, self.config["system_prompt"])
                
                if result.startswith("API调用失败") or result.startswith("API调用异常"):
                    self.log_message(f"失败: {result}")
                    self.log_message("批量处理已停止")
                    break
                else:
                    # 保存结果到文件
                    output_file = os.path.join(folder_b, f"{name_a}.txt")
                    try:
                        with open(output_file, 'w', encoding='utf-8') as f:
                            f.write(result)
                        self.log_message(f"成功: 结果已保存到 {output_file}")
                        processed_count += 1
                    except Exception as e:
                        self.log_message(f"保存文件失败: {str(e)}")
                        self.log_message("批量处理已停止")
                        break
            
            self.log_message(f"批量处理完成，共处理 {processed_count} 组图片")
            
        except Exception as e:
            self.log_message(f"批量处理异常: {str(e)}", "ERROR")
            self.progress_var.set("批量处理异常")
        finally:
            self.batch_generate_button.config(state="normal", text="🚀 开始批量处理")
    
    def on_closing(self):
        """窗口关闭事件处理"""
        self.root.destroy()

def main():
    root = tk.Tk()
    app = ImageComparisonTool(root)
    root.mainloop()

if __name__ == "__main__":
    main() 